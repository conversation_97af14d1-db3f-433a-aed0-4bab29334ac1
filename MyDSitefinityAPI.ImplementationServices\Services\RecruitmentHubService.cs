﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Mydentist.MyDSitefinityAPI.ImplementationServices.Interfaces;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net;
using System.Net.Http.Headers;
using System.Text;

namespace Mydentist.MyDSitefinityAPI.ImplementationServices.Services
{
    public class RecruitmentHubService : IRecruitmentHubService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<RecruitmentHubService> _logger;
        private readonly IConfiguration _configuration;

        public RecruitmentHubService(IHttpClientFactory httpClientFactory, ILogger<RecruitmentHubService> logger, IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory;
            _logger = logger;
            _configuration = configuration;
        }

        public async Task<bool> SendWebPayload(string originalJsonPayload)
        {
            // Deserialize the payload into a JObject
            JObject? payload = JsonConvert.DeserializeObject<JObject>(originalJsonPayload);

                try
                {

                if(payload?["CVUrl"] != null)
                {
                    // Get the CV file from the URL
                    string cvUrl = payload["CVUrl"].ToString();
                    byte[] fileData = await GetApplicantCv(cvUrl);

                    string base64FileData = Convert.ToBase64String(fileData);
                    string fileExtension = Path.GetExtension(new Uri(cvUrl).AbsolutePath);

                    // Create the new Resume object
                    var resume = new JObject
                    {
                        ["FileData"] = base64FileData,
                        ["FileExtension"] = fileExtension
                    };

                    // Update payload using JObject indexers
                    payload["Resume"] = resume;
                }
                    //payload["CVUrl"] = null;

                    string jsonPayload = JsonConvert.SerializeObject(payload);

                    HttpContent content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");

                    var httpClient = _httpClientFactory.CreateClient("RecruitmentHub");
                    httpClient.DefaultRequestHeaders.Add("APIKey", _configuration.GetSection("RecruitmentHub:ApiKey").Value);
                    var uri = _configuration.GetSection("RecruitmentHub:WebApply").Value;

                    var response = await httpClient.PostAsync(uri, content);

                    if (response != null && response.StatusCode == HttpStatusCode.Created)
                    {
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning($"Failed to send payload. Status code: {response?.StatusCode}");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error processing CV: {ex.Message}");
                    return false;
                }

            _logger.LogWarning("The payload does not contain a valid 'CVUrl'.");
            return false;
        }

        public async Task<byte[]> GetApplicantCv(string urlSource)
        {
            Uri uri = new Uri(urlSource);
            HttpClient httpClient = new HttpClient();
            string token = await GetWebsiteBearerToken();

            var httpRequestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Get,
                RequestUri = uri,
                Headers =
                {
                    {
                        HttpRequestHeader.Authorization.ToString(),
                        "Bearer " + token 
                    }
                }
            };

            HttpResponseMessage response = await httpClient.SendAsync(httpRequestMessage);
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadAsByteArrayAsync();
        }

        private async Task<string> GetWebsiteBearerToken()
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    // Hardcoded values this needs to be moved to appSettings
                    string clientId = "mydentistimport";
                    string clientSecret = "gKA9ruxfgX";
                    string username = "DataUpdate";
                    string password = "wZpFf847FaODf5hpXul2";
                    string tokenUrl = "https://www.mydentist.co.uk/sitefinity/oauth/token";
                    string grantType = "password";

                    var header = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{clientId}:{clientSecret}"));
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", header);

                    var content = new FormUrlEncodedContent(new[]
                    {
                        new KeyValuePair<string, string>("grant_type", grantType),
                        new KeyValuePair<string, string>("username", username),
                        new KeyValuePair<string, string>("client_id", clientId),
                        new KeyValuePair<string, string>("client_secret", clientSecret),
                        new KeyValuePair<string, string>("password", password)
                    });

                    // Send the POST request to get the token
                    var httpResponseMessage = await client.PostAsync(tokenUrl, content);

                    if (httpResponseMessage.IsSuccessStatusCode)
                    {
                        string responseBody = await httpResponseMessage.Content.ReadAsStringAsync();

                        _logger.LogInformation($"Successfully retrieved bearer token. HTTP Response: {httpResponseMessage.StatusCode}");

                        var tokenResponse = JsonConvert.DeserializeObject<dynamic>(responseBody);

                        if (tokenResponse != null && tokenResponse.access_token != null)
                        {
                            return tokenResponse.access_token;
                        }
                        else
                        {
                            _logger.LogError("Failed to retrieve access_token from the token response.");
                            return string.Empty; // Change to throw an exception
                        }
                    }
                    else
                    {
                        _logger.LogError($"Failed to retrieve bearer token. HTTP Status: {httpResponseMessage.StatusCode}, Reason: {httpResponseMessage.ReasonPhrase}");
                        return string.Empty; // Change to throw an exception
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception while retrieving bearer token: {ex.Message}");
                return string.Empty; // Change to throw an exception
            }
        }
    }
}
